/**
 * User Behavior Simulation Classes
 * 
 * Simulates realistic user behavior patterns for tokenomics stress testing.
 * Each user type has distinct behavior patterns that trigger real game functions.
 */

import fetch from 'node-fetch';
import { GAME_CONFIG } from '../../src/config/gameConfig.js';
import { TokenEconomyManager } from '../../src/managers/TokenEconomyManager.js';
import { ETHTestModeManager } from '../../src/managers/ETHTestModeManager.js';

export class UserSimulator {
    constructor({ type, account, config, id, transactionTracker, treasuryMonitor = null }) {
        this.type = type;
        this.account = account;
        this.config = config;
        this.id = id;
        this.transactionTracker = transactionTracker;
        this.treasuryMonitor = treasuryMonitor;

        // Mock localStorage for Node.js environment
        if (typeof global !== 'undefined' && !global.localStorage) {
            global.localStorage = {
                getItem: () => null,
                setItem: () => {},
                removeItem: () => {},
                clear: () => {}
            };
        }

        // Initialize real game managers for accurate economics
        this.tokenEconomyManager = new TokenEconomyManager();

        // Connect wallet to initialize dailyRewardTracker
        this.tokenEconomyManager.walletAddress = this.account.address;
        this.tokenEconomyManager.walletConnected = true;
        this.tokenEconomyManager.initializeDailyRewardTracker();

        this.ethTestModeManager = new ETHTestModeManager(this.tokenEconomyManager);

        // Behavior configuration based on user type
        this.behaviorConfig = this.getBehaviorConfig(type);

        // State tracking
        this.sessionData = {
            transactions: [],
            rewards: [],
            purchases: [],
            environments: [],
            startTime: null,
            endTime: null
        };

        console.log(`👤 Created ${type} user simulator: ${id} (${account.address})`);
    }

    /**
     * Get behavior configuration for user type
     */
    getBehaviorConfig(type) {
        const configs = {
            grinder: {
                playPattern: 'max_completion',
                spendingBehavior: 'zero_spending',
                rewardEarning: 'maximum',
                sessionDuration: 180000, // 3 minutes
                levelsPerSession: 10,
                completionRate: 1.0, // 100% completion
                purchaseProbability: 0.0, // Never purchases
                maxRewardMultiplier: 1.25 * 1.125 * 1.075 // All bonuses
            },
            whale: {
                playPattern: 'minimal_grinding',
                spendingBehavior: 'heavy_purchasing',
                rewardEarning: 'minimal',
                sessionDuration: 60000, // 1 minute
                levelsPerSession: 2,
                completionRate: 0.3, // 30% completion
                purchaseProbability: 0.8, // 80% purchase rate
                maxRewardMultiplier: 1.0 // No bonuses
            },
            creator: {
                playPattern: 'content_creation',
                spendingBehavior: 'environment_purchases',
                rewardEarning: 'creator_rewards',
                sessionDuration: 120000, // 2 minutes
                levelsPerSession: 5,
                completionRate: 0.6, // 60% completion
                purchaseProbability: 0.3, // 30% purchase rate
                environmentCreationRate: 0.7, // 70% chance to create environment
                creatorRewardRate: 0.5 // 50% of environment sales
            },
            casual: {
                playPattern: 'moderate_play',
                spendingBehavior: 'occasional_powerups',
                rewardEarning: 'moderate',
                sessionDuration: 150000, // 2.5 minutes
                levelsPerSession: 7,
                completionRate: 0.6, // 60% completion
                purchaseProbability: 0.2, // 20% purchase rate
                maxRewardMultiplier: 1.125 // Some bonuses
            }
        };

        return configs[type] || configs.casual;
    }

    /**
     * Simulate a complete gaming session
     */
    async simulateGamingSession() {
        try {
            console.log(`🎮 Starting ${this.type} session for ${this.id}...`);
            this.sessionData.startTime = Date.now();

            // Authenticate with the game server
            await this.authenticateWithServer();

            // Execute behavior pattern based on user type
            switch (this.type) {
                case 'grinder':
                    await this.simulateGrindingSession();
                    break;
                case 'whale':
                    await this.simulateWhaleSession();
                    break;
                case 'creator':
                    await this.simulateCreatorSession();
                    break;
                case 'casual':
                    await this.simulateCasualSession();
                    break;
            }

            this.sessionData.endTime = Date.now();
            console.log(`✅ Completed ${this.type} session for ${this.id}`);

        } catch (error) {
            console.error(`❌ Error in ${this.type} session for ${this.id}:`, error);
            throw error;
        }
    }

    /**
     * Authenticate with the game server (simulated)
     */
    async authenticateWithServer() {
        // In a real implementation, this would handle OrangeID authentication
        // For testing, we'll simulate successful authentication
        console.log(`🔐 Authenticated ${this.id} with server`);
    }

    /**
     * Simulate grinder behavior - max rewards, zero spending using REAL game economics
     */
    async simulateGrindingSession() {
        const { levelsPerSession } = this.behaviorConfig;

        for (let level = 1; level <= levelsPerSession; level++) {
            // Simulate perfect level completion using REAL TokenEconomyManager
            const completionData = {
                completed: true,
                levelNumber: level,
                totalEnemies: 50, // Typical level enemy count
                enemiesDefeated: 50 // Perfect completion (100%)
            };

            const levelConfig = {
                totalEnemies: 50
            };

            // Use REAL reward calculation from TokenEconomyManager
            const rewardData = this.tokenEconomyManager.calculateLevelReward(completionData, levelConfig);

            if (rewardData.totalReward > 0) {
                // Convert WISH tokens to ETH using REAL ETH Test Mode (90% discount)
                const wishTokens = rewardData.totalReward;
                const ethAmount = this.ethTestModeManager.applyTestModeDiscount(wishTokens) / 100; // Convert to ETH

                console.log(`🎮 Level ${level}: ${wishTokens} WISH → ${ethAmount} ETH (90% discount applied)`);

                // Record the level completion in daily tracker
                this.tokenEconomyManager.dailyRewardTracker.recordLevelCompletion(
                    level,
                    rewardData.breakdown.completionPercentage,
                    rewardData.totalReward
                );

                // Award the REAL calculated reward as ETH
                await this.awardTokens(ethAmount, `Level ${level} completion - ${(rewardData.breakdown.completionPercentage * 100).toFixed(0)}% enemies defeated`);
            } else {
                console.log(`🚫 Level ${level}: No reward (${rewardData.breakdown.reason})`);
            }

            // Small delay between levels
            await this.delay(500);
        }

        console.log(`🏆 Grinder ${this.id} completed ${levelsPerSession} levels with REAL rewards`);
    }

    /**
     * Simulate whale behavior - heavy spending, minimal grinding
     */
    async simulateWhaleSession() {
        const { levelsPerSession, purchaseProbability } = this.behaviorConfig;

        // Heavy purchasing before playing
        await this.simulateHeavyPurchasing();

        // Minimal grinding with purchased power-ups
        for (let level = 1; level <= levelsPerSession; level++) {
            // Minimal rewards due to poor performance
            const minimalReward = Math.floor(1250 * level * 0.3); // 30% of base reward
            await this.awardTokens(minimalReward, `Level ${level} completion - minimal effort`);

            await this.delay(1000);
        }

        console.log(`💰 Whale ${this.id} completed session with heavy spending`);
    }

    /**
     * Simulate creator behavior - environment creation and moderate play
     */
    async simulateCreatorSession() {
        // Create custom environments
        await this.simulateEnvironmentCreation();

        // Moderate gameplay
        await this.simulateModerateGameplay();

        console.log(`🎨 Creator ${this.id} completed session with environment creation`);
    }

    /**
     * Simulate casual player behavior
     */
    async simulateCasualSession() {
        const { levelsPerSession, purchaseProbability } = this.behaviorConfig;

        // Occasional purchases
        if (Math.random() < purchaseProbability) {
            await this.simulateOccasionalPurchase();
        }

        // Moderate gameplay
        await this.simulateModerateGameplay();

        console.log(`🎯 Casual player ${this.id} completed session`);
    }

    /**
     * Simulate environment creation (creator behavior)
     */
    async simulateEnvironmentCreation() {
        const startTime = Date.now();
        try {
            const environmentDescription = this.generateRandomEnvironmentDescription();

            console.log(`🎨 ${this.id} creating environment: "${environmentDescription}"`);

            const requestData = {
                environmentDescription,
                creatorUserId: this.id,
                creatorAddress: this.account.address
            };

            const response = await fetch(`${this.config.apiBaseUrl}/generate-environment`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.config.apiAuthToken || 'test-token'}`
                },
                body: JSON.stringify(requestData)
            });

            const duration = Date.now() - startTime;

            if (!response.ok) {
                const error = new Error(`Environment creation failed: ${response.status}`);
                // Track failed API call
                if (this.transactionTracker) {
                    this.transactionTracker.trackAPICall('/generate-environment', 'POST', requestData, null, duration, error);
                }
                throw error;
            }

            const environment = await response.json();
            this.sessionData.environments.push(environment);

            // Track successful API call and environment creation
            if (this.transactionTracker) {
                this.transactionTracker.trackAPICall('/generate-environment', 'POST', requestData, environment, duration);
                this.transactionTracker.trackEnvironmentCreation(this.id, environment, 0, 0);
            }

            console.log(`✅ Environment created by ${this.id}: ${environment.name}`);
            console.log(`✅ Environment created: ${environment.name} by ${this.id}`);
            return environment;

        } catch (error) {
            console.error(`❌ Environment creation failed for ${this.id}:`, error);
            throw error;
        }
    }

    /**
     * Simulate mystical environment purchases (triggers creator rewards)
     */
    async simulateMysticalEnvironmentPurchases() {
        try {
            const purchaseAmount = 25000; // 25,000 WISH tokens (ETH Test Mode: 2,500 ETH)
            
            console.log(`💰 ${this.id} purchasing Mystical Environment for ${purchaseAmount} tokens`);

            // Spend tokens for environment purchase
            await this.spendTokens(purchaseAmount, 'Mystical Environment Purchase');

            // This should trigger 50% creator reward distribution
            console.log(`🎁 Mystical Environment purchase should trigger 50% creator reward`);

        } catch (error) {
            console.error(`❌ Mystical environment purchase failed for ${this.id}:`, error);
            throw error;
        }
    }

    /**
     * Simulate heavy purchasing (whale behavior) using REAL game prices
     */
    async simulateHeavyPurchasing() {
        // Use REAL power-up costs from GAME_CONFIG
        const realPurchases = [
            { wishCost: GAME_CONFIG.POWER_UP_COSTS.REALITY_WARP, item: 'Reality Warp' },
            { wishCost: GAME_CONFIG.POWER_UP_COSTS.EXTRA_LIFE, item: 'Extra Life' },
            { wishCost: GAME_CONFIG.POWER_UP_COSTS.EXTRA_WINGMAN, item: 'Extra Wingman' },
            { wishCost: GAME_CONFIG.POWER_UP_COSTS.SPREAD_AMMO, item: 'Spread Ammo' }
        ];

        for (const purchase of realPurchases) {
            // Apply ETH Test Mode discount (90% off)
            const discountedWishCost = this.ethTestModeManager.applyTestModeDiscount(purchase.wishCost);
            const ethCost = discountedWishCost / 100; // Convert to ETH

            console.log(`🛒 ${purchase.item}: ${purchase.wishCost} WISH → ${discountedWishCost} WISH (90% off) → ${ethCost} ETH`);

            await this.spendTokens(ethCost, purchase.item);
            await this.delay(200);
        }
    }

    /**
     * Simulate occasional purchase (casual behavior) using REAL game prices
     */
    async simulateOccasionalPurchase() {
        // Use REAL power-up costs from GAME_CONFIG (casual players buy cheaper items)
        const casualPurchases = [
            { wishCost: GAME_CONFIG.POWER_UP_COSTS.SPREAD_AMMO, item: 'Spread Ammo' },
            { wishCost: GAME_CONFIG.POWER_UP_COSTS.EXTRA_WINGMAN, item: 'Extra Wingman' },
            { wishCost: GAME_CONFIG.POWER_UP_COSTS.EXTRA_LIFE, item: 'Extra Life' }
        ];

        const randomPurchase = casualPurchases[Math.floor(Math.random() * casualPurchases.length)];

        // Apply ETH Test Mode discount (90% off)
        const discountedWishCost = this.ethTestModeManager.applyTestModeDiscount(randomPurchase.wishCost);
        const ethCost = discountedWishCost / 100; // Convert to ETH

        console.log(`🛒 Casual ${randomPurchase.item}: ${randomPurchase.wishCost} WISH → ${ethCost} ETH`);

        await this.spendTokens(ethCost, randomPurchase.item);
    }

    /**
     * Simulate moderate gameplay
     */
    async simulateModerateGameplay() {
        const { levelsPerSession, completionRate, maxRewardMultiplier } = this.behaviorConfig;

        for (let level = 1; level <= levelsPerSession; level++) {
            const baseReward = 1250;
            const reward = Math.floor(
                baseReward * level * completionRate * (maxRewardMultiplier || 1.0)
            );

            await this.awardTokens(reward, `Level ${level} completion`);
            await this.delay(800);
        }
    }

    /**
     * Award tokens to user - REAL ETH TRANSFER from hot wallet
     */
    async awardTokens(ethAmount, reason) {
        const startTime = Date.now();
        try {
            console.log(`🎁 Awarding ${ethAmount} ETH to ${this.id} for: ${reason}`);

            // Make REAL blockchain transaction from hot wallet to user
            const requestData = {
                toAddress: this.account.address,
                amount: ethAmount.toString(),
                reason: `ETH reward: ${reason}`
            };

            const response = await fetch(`${this.config.apiBaseUrl}/wallet/send`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer secure-token-for-development`
                },
                body: JSON.stringify(requestData)
            });

            const duration = Date.now() - startTime;

            if (!response.ok) {
                const errorText = await response.text();
                const error = new Error(`Real ETH transfer failed: ${response.status} - ${errorText}`);
                // Track failed API call
                if (this.transactionTracker) {
                    this.transactionTracker.trackAPICall('/wallet/send', 'POST', requestData, null, duration, error);
                }
                throw error;
            }

            const result = await response.json();
            this.sessionData.rewards.push(result);

            // Track this REAL blockchain transaction
            if (this.transactionTracker) {
                this.transactionTracker.trackAPICall('/wallet/send', 'POST', requestData, result, duration);
                this.transactionTracker.trackETHTransfer(
                    this.config.hotWalletAddress,
                    this.account.address,
                    ethAmount,
                    `Token reward: ${reason}`,
                    result.transactionHash,
                    { ethAmount: ethAmount, userType: this.type }
                );
            }

            console.log(`✅ REAL ETH transfer completed: ${ethAmount} ETH to ${this.account.address}`);
            console.log(`📤 Transaction hash: ${result.transactionHash}`);

            // Notify TreasuryMonitor about the outflow
            if (this.treasuryMonitor) {
                this.treasuryMonitor.recordOutflow(parseFloat(ethAmount), `Reward to ${this.id}: ${reason}`);
            }

            // Track the transaction in TransactionTracker
            if (this.transactionTracker) {
                console.log(`🔍 Tracking token award: ${this.id}, ${ethAmount} ETH, ${result.transactionHash}`);
                this.transactionTracker.trackTokenAward(
                    this.id,
                    parseFloat(ethAmount),
                    reason,
                    result.transactionHash,
                    {
                        userType: this.type,
                        walletAddress: this.account.address,
                        ethAmount: parseFloat(ethAmount)
                    }
                );
            } else {
                console.log(`⚠️ No TransactionTracker available for ${this.id}`);
            }

            return result;

        } catch (error) {
            console.error(`❌ Real ETH transfer failed for ${this.id}:`, error);
            throw error;
        }
    }

    /**
     * Spend tokens from user - REAL CREATOR REWARD DISTRIBUTION
     */
    async spendTokens(ethAmount, reason) {
        const startTime = Date.now();
        try {
            console.log(`💸 ${this.id} spending ${ethAmount} ETH for: ${reason}`);

            const mockResult = {
                success: true,
                transactionId: `txn_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
                userId: this.id,
                amount: ethAmount,
                reason: reason,
                timestamp: Date.now()
            };

            this.sessionData.purchases.push(mockResult);

            // Track the spend transaction
            if (this.transactionTracker) {
                this.transactionTracker.trackTokenSpend(this.id, ethAmount, reason, mockResult.transactionId, {
                    userType: this.type,
                    walletAddress: this.account.address
                });
            }

            // If this is a Mystical Environment purchase, make REAL creator reward ETH transfer
            if (reason && reason.includes('Mystical Environment')) {
                const creatorRewardETH = (ethAmount * 0.5).toString(); // 50% goes to creator

                console.log(`🎨 Mystical Environment purchase detected - distributing ${creatorRewardETH} ETH to creator`);

                // Make REAL ETH transfer to creator using rewards/distribute endpoint
                const rewardRequestData = {
                    creatorUserId: this.account.address, // Use purchaser's address as creator for testing
                    amount: creatorRewardETH,
                    reason: `Creator reward for ${reason} (50% of ${ethAmount} ETH)`
                };

                const rewardResponse = await fetch(`${this.config.apiBaseUrl}/rewards/distribute`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer secure-token-for-development`
                    },
                    body: JSON.stringify(rewardRequestData)
                });

                const rewardDuration = Date.now() - startTime;

                if (!rewardResponse.ok) {
                    const errorText = await rewardResponse.text();
                    console.error(`❌ Creator reward distribution failed: ${rewardResponse.status} - ${errorText}`);
                } else {
                    const rewardResult = await rewardResponse.json();

                    // Track the REAL creator reward transaction
                    if (this.transactionTracker) {
                        this.transactionTracker.trackAPICall('/rewards/distribute', 'POST', rewardRequestData, rewardResult, rewardDuration);
                        this.transactionTracker.trackETHTransfer(
                            this.config.hotWalletAddress,
                            this.account.address,
                            creatorRewardETH,
                            `Creator reward for ${reason}`,
                            rewardResult.walletTransaction?.transactionHash || `reward_${mockResult.transactionId}`,
                            {
                                originalPurchase: mockResult.transactionId,
                                purchaseAmount: ethAmount,
                                rewardPercentage: 50,
                                ethAmount: creatorRewardETH
                            }
                        );
                    }

                    console.log(`✅ REAL creator reward distributed: ${creatorRewardETH} ETH`);
                    console.log(`📤 Reward transaction hash: ${rewardResult.walletTransaction?.transactionHash}`);
                }
            }

            console.log(`💸 ${this.id} completed spend transaction for: ${reason}`);
            return mockResult;

        } catch (error) {
            console.error(`❌ Token spend transaction failed for ${this.id}:`, error);
            throw error;
        }
    }

    /**
     * Generate random environment description for creators
     */
    generateRandomEnvironmentDescription() {
        const themes = [
            'Mystical crystal caverns with floating islands',
            'Cyberpunk neon cityscape with rain',
            'Ancient temple ruins in a jungle',
            'Underwater coral reef with bioluminescence',
            'Volcanic landscape with lava flows',
            'Ice planet with aurora borealis',
            'Desert oasis with sandstorms',
            'Space station with nebula backdrop'
        ];

        return themes[Math.floor(Math.random() * themes.length)];
    }

    /**
     * Simulate coordinated behavior across multiple accounts
     */
    async simulateCoordinatedBehavior() {
        console.log(`🤝 ${this.id} starting coordinated behavior simulation`);
        
        // Simulate coordinated actions that might stress the system
        await this.simulateGamingSession();
        
        // Add small random delay to avoid perfect synchronization
        await this.delay(Math.random() * 2000);
    }

    /**
     * Simulate maximum stress behavior
     */
    async simulateMaxStressBehavior() {
        console.log(`⚡ ${this.id} starting maximum stress behavior`);
        
        // Rapid-fire actions to stress test the system
        const promises = [];
        
        // Multiple concurrent actions
        if (this.type === 'grinder') {
            promises.push(this.simulateGrindingSession());
        } else if (this.type === 'whale') {
            promises.push(this.simulateHeavyPurchasing());
        } else if (this.type === 'creator') {
            promises.push(this.simulateEnvironmentCreation());
        }
        
        await Promise.all(promises);
    }

    /**
     * Get session summary
     */
    getSessionSummary() {
        return {
            userId: this.id,
            userType: this.type,
            account: this.account.address,
            sessionDuration: this.sessionData.endTime - this.sessionData.startTime,
            totalRewards: this.sessionData.rewards.reduce((sum, r) => sum + r.amount, 0),
            totalSpent: this.sessionData.purchases.reduce((sum, p) => sum + p.amount, 0),
            transactionCount: this.sessionData.transactions.length,
            environmentsCreated: this.sessionData.environments.length
        };
    }

    /**
     * Utility method for delays
     */
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
