/**
 * Validation System for Tokenomics Stress Testing
 * 
 * Provides comprehensive validation of test results against expected behaviors
 * and economic sustainability criteria.
 */

export class ValidationSystem {
    constructor(config = {}) {
        this.config = config;
        this.validationResults = [];
        this.criticalIssues = [];
        this.warnings = [];
        this.recommendations = [];
    }

    /**
     * Validate complete stress test results
     */
    async validateStressTestResults(report, transactionData, treasuryData) {
        console.log('🔍 Starting comprehensive validation...');
        
        this.validationResults = [];
        this.criticalIssues = [];
        this.warnings = [];
        this.recommendations = [];

        // Run all validation checks
        await this.validateTreasurySustainability(treasuryData);
        await this.validateCreatorRewardDistribution(transactionData);
        await this.validateGrinderBehavior(transactionData);
        await this.validateTransactionIntegrity(transactionData);
        await this.validateEconomicBalance(report, transactionData, treasuryData);
        await this.validateSystemStability(report);

        // Generate final validation report
        const validationReport = this.generateValidationReport();
        
        console.log('✅ Validation completed');
        return validationReport;
    }

    /**
     * Validate treasury sustainability
     */
    async validateTreasurySustainability(treasuryData) {
        console.log('💰 Validating treasury sustainability...');

        const status = treasuryData.treasuryStatus;
        const analysis = treasuryData.flowAnalysis;

        console.log('🔍 Treasury Status Debug:', {
            currentBalance: status.currentBalance,
            initialBalance: status.initialBalance,
            balanceChange: status.balanceChange,
            balanceChangePercent: status.balanceChangePercent,
            netFlow: status.netFlow,
            riskLevel: status.riskLevel
        });

        // Check 1: Treasury balance should remain positive
        const balanceCheck = {
            test: 'Treasury Balance Positive',
            passed: status.currentBalance > 0,
            value: `${status.currentBalance.toFixed(6)} ETH`,
            expected: '> 0 ETH',
            severity: status.currentBalance <= 0 ? 'critical' : 'pass'
        };
        this.addValidationResult(balanceCheck);

        // Check 2: Balance change should not exceed 50% of initial balance (fix Infinity% issue)
        let balanceChangePercent = 0;
        if (status.initialBalance && status.initialBalance > 0) {
            balanceChangePercent = Math.abs((status.currentBalance - status.initialBalance) / status.initialBalance * 100);
        }
        const balanceChangeCheck = {
            test: 'Treasury Balance Change Reasonable',
            passed: balanceChangePercent <= 50,
            value: `${balanceChangePercent.toFixed(2)}%`,
            expected: '≤ 50%',
            severity: balanceChangePercent > 50 ? 'critical' : 'pass'
        };
        this.addValidationResult(balanceChangeCheck);

        // Check 3: Net flow should not be extremely negative (fix Infinity% issue)
        let netFlowRatio = 0;
        if (status.initialBalance && status.initialBalance > 0) {
            netFlowRatio = status.netFlow / status.initialBalance;
        }
        const netFlowCheck = {
            test: 'Net Flow Sustainable',
            passed: netFlowRatio > -0.8, // Not more than 80% outflow
            value: `${(netFlowRatio * 100).toFixed(2)}%`,
            expected: '> -80%',
            severity: netFlowRatio <= -0.8 ? 'critical' : netFlowRatio <= -0.5 ? 'warning' : 'pass'
        };
        this.addValidationResult(netFlowCheck);

        // Check 4: Risk level should not be critical
        const riskCheck = {
            test: 'Treasury Risk Level Acceptable',
            passed: status.riskLevel !== 'critical',
            value: status.riskLevel,
            expected: 'not critical',
            severity: status.riskLevel === 'critical' ? 'critical' : status.riskLevel === 'high' ? 'warning' : 'pass'
        };
        this.addValidationResult(riskCheck);
    }

    /**
     * Validate creator reward distribution (50% accuracy)
     */
    async validateCreatorRewardDistribution(transactionData) {
        console.log('🎨 Validating creator reward distribution...');
        
        const creatorAnalysis = transactionData.creatorRewardAnalysis;
        
        // Check 1: Creator rewards should be distributed
        const rewardsDistributedCheck = {
            test: 'Creator Rewards Distributed',
            passed: creatorAnalysis.totalCreatorRewards > 0,
            value: `${creatorAnalysis.totalCreatorRewards} ETH`,
            expected: '> 0 ETH',
            severity: creatorAnalysis.totalCreatorRewards === 0 ? 'warning' : 'pass'
        };
        this.addValidationResult(rewardsDistributedCheck);

        console.log('🔍 Creator Analysis Debug:', {
            totalCreatorRewards: creatorAnalysis.totalCreatorRewards,
            rewardDistributionAccuracy: creatorAnalysis.rewardDistributionAccuracy,
            environmentPurchases: creatorAnalysis.environmentPurchases,
            creatorRewardTransactions: creatorAnalysis.creatorRewardTransactions
        });

        // Check 2: Reward distribution accuracy should be close to 50% (only if there were purchases)
        const accuracy = parseFloat(creatorAnalysis.rewardDistributionAccuracy);
        const hasPurchases = creatorAnalysis.environmentPurchases > 0;

        if (hasPurchases) {
            const accuracyCheck = {
                test: 'Creator Reward Accuracy (50%)',
                passed: accuracy >= 45 && accuracy <= 55, // 5% tolerance
                value: `${accuracy}%`,
                expected: '45-55%',
                severity: accuracy < 40 || accuracy > 60 ? 'critical' : accuracy < 45 || accuracy > 55 ? 'warning' : 'pass'
            };
            this.addValidationResult(accuracyCheck);
        } else {
            // Skip accuracy check if no purchases were made (grinder scenario)
            const accuracyCheck = {
                test: 'Creator Reward Accuracy (50%)',
                passed: true, // Pass if no purchases to validate
                value: 'N/A (no purchases)',
                expected: '45-55% (when purchases occur)',
                severity: 'pass'
            };
            this.addValidationResult(accuracyCheck);
        }

        // Check 3: Environment purchases should trigger rewards (only if purchases were made)
        const purchaseToRewardRatio = creatorAnalysis.environmentPurchases > 0
            ? creatorAnalysis.creatorRewardTransactions / creatorAnalysis.environmentPurchases
            : 0;

        if (hasPurchases) {
            const purchaseRewardCheck = {
                test: 'Environment Purchases Trigger Rewards',
                passed: purchaseToRewardRatio >= 0.8, // At least 80% of purchases should trigger rewards
                value: `${(purchaseToRewardRatio * 100).toFixed(1)}%`,
                expected: '≥ 80%',
                severity: purchaseToRewardRatio < 0.5 ? 'critical' : purchaseToRewardRatio < 0.8 ? 'warning' : 'pass'
            };
            this.addValidationResult(purchaseRewardCheck);
        } else {
            // Skip if no environment purchases were made (grinder scenario doesn't make purchases)
            const purchaseRewardCheck = {
                test: 'Environment Purchases Trigger Rewards',
                passed: true, // Pass if no purchases to validate
                value: 'N/A (no environment purchases)',
                expected: '≥ 80% (when purchases occur)',
                severity: 'pass'
            };
            this.addValidationResult(purchaseRewardCheck);
        }
    }

    /**
     * Validate grinder behavior (max rewards, zero spending)
     */
    async validateGrinderBehavior(transactionData) {
        console.log('🎮 Validating grinder behavior...');
        
        console.log('🔍 User Activity Debug:', {
            userActivity: transactionData.userActivity,
            hasUserActivity: !!transactionData.userActivity,
            userActivityKeys: transactionData.userActivity ? Object.keys(transactionData.userActivity) : 'none'
        });

        const userActivity = transactionData.userActivity || {};
        const grinders = Object.entries(userActivity).filter(([userId, data]) =>
            userId.includes('grinder')
        );

        if (grinders.length === 0) {
            this.addValidationResult({
                test: 'Grinder Users Present',
                passed: Object.keys(userActivity).length === 0, // Pass if no user activity tracked at all
                value: Object.keys(userActivity).length === 0 ? 'No user activity tracked' : '0 grinders',
                expected: '> 0 grinders (when user activity is tracked)',
                severity: Object.keys(userActivity).length > 0 ? 'warning' : 'pass'
            });
            return;
        }

        // Check each grinder
        grinders.forEach(([userId, data]) => {
            // Check 1: Grinder should earn maximum rewards
            const rewardCheck = {
                test: `Grinder ${userId} - Maximum Rewards`,
                passed: data.tokensAwarded > 0,
                value: `${data.tokensAwarded} tokens`,
                expected: '> 0 tokens',
                severity: data.tokensAwarded === 0 ? 'warning' : 'pass'
            };
            this.addValidationResult(rewardCheck);

            // Check 2: Grinder should spend zero tokens
            const spendingCheck = {
                test: `Grinder ${userId} - Zero Spending`,
                passed: data.tokensSpent === 0,
                value: `${data.tokensSpent} tokens`,
                expected: '0 tokens',
                severity: data.tokensSpent > 0 ? 'critical' : 'pass'
            };
            this.addValidationResult(spendingCheck);

            // Check 3: Grinder should have high transaction count (active grinding)
            const activityCheck = {
                test: `Grinder ${userId} - High Activity`,
                passed: data.totalTransactions >= 5, // At least 5 transactions expected
                value: `${data.totalTransactions} transactions`,
                expected: '≥ 5 transactions',
                severity: data.totalTransactions < 3 ? 'warning' : 'pass'
            };
            this.addValidationResult(activityCheck);
        });
    }

    /**
     * Validate transaction integrity
     */
    async validateTransactionIntegrity(transactionData) {
        console.log('🔗 Validating transaction integrity...');

        console.log('🔍 Transaction Data Debug:', {
            totalTransactions: transactionData.totalTransactions,
            transactions: transactionData.transactions ? transactionData.transactions.length : 'undefined',
            totalRewards: transactionData.totalRewards,
            totalSpends: transactionData.totalSpends,
            summary: transactionData.summary,
            metrics: transactionData.metrics
        });

        const summary = transactionData.summary;
        const metrics = transactionData.metrics;

        // Check 0: Transactions should be recorded
        const actualTransactionCount = transactionData.transactions ? transactionData.transactions.length : (transactionData.totalTransactions || 0);
        const transactionsRecordedCheck = {
            test: 'Test Completion',
            passed: actualTransactionCount > 0,
            value: `${actualTransactionCount} transactions`,
            expected: '> 0 transactions',
            severity: actualTransactionCount === 0 ? 'critical' : 'pass'
        };
        this.addValidationResult(transactionsRecordedCheck);

        // Check 1: Transaction success rate should be high
        const errorRate = parseFloat(metrics.errorRate);
        const errorRateCheck = {
            test: 'Transaction Error Rate Low',
            passed: errorRate <= 10, // Less than 10% error rate
            value: `${errorRate}%`,
            expected: '≤ 10%',
            severity: errorRate > 20 ? 'critical' : errorRate > 10 ? 'warning' : 'pass'
        };
        this.addValidationResult(errorRateCheck);

        // Check 2: Response times should be reasonable
        const avgResponseTime = metrics.averageResponseTime;
        const responseTimeCheck = {
            test: 'Average Response Time Reasonable',
            passed: avgResponseTime <= 5000, // Less than 5 seconds
            value: `${avgResponseTime}ms`,
            expected: '≤ 5000ms',
            severity: avgResponseTime > 10000 ? 'critical' : avgResponseTime > 5000 ? 'warning' : 'pass'
        };
        this.addValidationResult(responseTimeCheck);

        // Check 3: Token awards and spends should balance
        const tokenBalance = summary.token_award.totalAmount - summary.token_spend.totalAmount;
        const tokenBalanceCheck = {
            test: 'Token Flow Balance',
            passed: Math.abs(tokenBalance) < summary.token_award.totalAmount * 0.1, // Within 10%
            value: `${tokenBalance} tokens`,
            expected: 'Balanced flow',
            severity: 'info'
        };
        this.addValidationResult(tokenBalanceCheck);
    }

    /**
     * Validate economic balance
     */
    async validateEconomicBalance(report, transactionData, treasuryData) {
        console.log('⚖️ Validating economic balance...');
        
        const sustainability = report.sustainabilityAssessment;
        const treasury = treasuryData.treasuryStatus;

        // Check 1: System should be economically sustainable
        const sustainabilityCheck = {
            test: 'Economic Sustainability',
            passed: sustainability.isSustainable,
            value: sustainability.isSustainable ? 'Sustainable' : 'Not Sustainable',
            expected: 'Sustainable',
            severity: !sustainability.isSustainable ? 'critical' : 'pass'
        };
        this.addValidationResult(sustainabilityCheck);

        // Check 2: Burn rate should not be excessive
        const burnRate = treasury.burnRate || 0;
        const burnRateCheck = {
            test: 'Treasury Burn Rate Reasonable',
            passed: burnRate <= 0.01, // Less than 0.01 ETH per second
            value: `${burnRate.toFixed(6)} ETH/s`,
            expected: '≤ 0.01 ETH/s',
            severity: burnRate > 0.05 ? 'critical' : burnRate > 0.01 ? 'warning' : 'pass'
        };
        this.addValidationResult(burnRateCheck);

        // Check 3: Projected runtime should be reasonable
        const projectedRuntime = treasury.projectedRuntime;
        const runtimeCheck = {
            test: 'Projected Treasury Runtime',
            passed: projectedRuntime === Infinity || projectedRuntime > 3600, // More than 1 hour
            value: projectedRuntime === Infinity ? 'Infinite' : `${Math.floor(projectedRuntime / 60)} minutes`,
            expected: '> 60 minutes',
            severity: projectedRuntime < 1800 ? 'critical' : projectedRuntime < 3600 ? 'warning' : 'pass'
        };
        this.addValidationResult(runtimeCheck);
    }

    /**
     * Validate system stability
     */
    async validateSystemStability(report) {
        console.log('🔧 Validating system stability...');
        
        const testSummary = report.testSummary;

        // Check 1: Test should complete without major failures
        const completionCheck = {
            test: 'Test Completion',
            passed: testSummary.totalTransactions > 0,
            value: `${testSummary.totalTransactions} transactions`,
            expected: '> 0 transactions',
            severity: testSummary.totalTransactions === 0 ? 'critical' : 'pass'
        };
        this.addValidationResult(completionCheck);

        // Check 2: Multiple user types should be active
        const userCheck = {
            test: 'Multi-User Activity',
            passed: testSummary.totalUsers >= 4, // At least 4 different user types
            value: `${testSummary.totalUsers} users`,
            expected: '≥ 4 users',
            severity: testSummary.totalUsers < 2 ? 'critical' : testSummary.totalUsers < 4 ? 'warning' : 'pass'
        };
        this.addValidationResult(userCheck);
    }

    /**
     * Add validation result
     */
    addValidationResult(result) {
        this.validationResults.push(result);
        
        if (result.severity === 'critical') {
            this.criticalIssues.push(result);
        } else if (result.severity === 'warning') {
            this.warnings.push(result);
        }
    }

    /**
     * Generate comprehensive validation report
     */
    generateValidationReport() {
        const totalTests = this.validationResults.length;
        const passedTests = this.validationResults.filter(r => r.passed).length;
        const failedTests = totalTests - passedTests;
        
        const report = {
            summary: {
                totalTests,
                passedTests,
                failedTests,
                passRate: totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0,
                criticalIssues: this.criticalIssues.length,
                warnings: this.warnings.length
            },
            overallResult: this.criticalIssues.length === 0 ? 'PASS' : 'FAIL',
            validationResults: this.validationResults,
            criticalIssues: this.criticalIssues,
            warnings: this.warnings,
            recommendations: this.generateRecommendations(),
            timestamp: Date.now()
        };

        this.logValidationSummary(report);
        return report;
    }

    /**
     * Generate recommendations based on validation results
     */
    generateRecommendations() {
        const recommendations = [];

        // Critical issues recommendations
        if (this.criticalIssues.length > 0) {
            recommendations.push('CRITICAL: Address all critical issues before production deployment');
            
            this.criticalIssues.forEach(issue => {
                if (issue.test.includes('Treasury Balance')) {
                    recommendations.push('Increase initial treasury funding or reduce reward payouts');
                } else if (issue.test.includes('Creator Reward Accuracy')) {
                    recommendations.push('Fix creator reward distribution logic to ensure 50% accuracy');
                } else if (issue.test.includes('Grinder') && issue.test.includes('Spending')) {
                    recommendations.push('Verify grinder behavior - they should not spend any tokens');
                } else if (issue.test.includes('Error Rate')) {
                    recommendations.push('Investigate and fix transaction processing errors');
                }
            });
        }

        // Warning-based recommendations
        if (this.warnings.length > 0) {
            recommendations.push('Review warning items for potential improvements');
        }

        // General recommendations
        if (this.validationResults.filter(r => r.passed).length === this.validationResults.length) {
            recommendations.push('All validation tests passed - system appears ready for production');
        }

        return recommendations;
    }

    /**
     * Log validation summary to console
     */
    logValidationSummary(report) {
        console.log('');
        console.log('🔍 VALIDATION RESULTS');
        console.log('=' .repeat(50));
        console.log(`Overall Result: ${report.overallResult === 'PASS' ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`Tests Passed: ${report.summary.passedTests}/${report.summary.totalTests} (${report.summary.passRate}%)`);
        console.log(`Critical Issues: ${report.summary.criticalIssues}`);
        console.log(`Warnings: ${report.summary.warnings}`);
        console.log('');

        // Show critical issues
        if (this.criticalIssues.length > 0) {
            console.log('❌ CRITICAL ISSUES:');
            this.criticalIssues.forEach((issue, index) => {
                console.log(`   ${index + 1}. ${issue.test}: ${issue.value} (expected: ${issue.expected})`);
            });
            console.log('');
        }

        // Show warnings
        if (this.warnings.length > 0) {
            console.log('⚠️ WARNINGS:');
            this.warnings.forEach((warning, index) => {
                console.log(`   ${index + 1}. ${warning.test}: ${warning.value} (expected: ${warning.expected})`);
            });
            console.log('');
        }

        // Show recommendations
        if (report.recommendations.length > 0) {
            console.log('💡 RECOMMENDATIONS:');
            report.recommendations.forEach((rec, index) => {
                console.log(`   ${index + 1}. ${rec}`);
            });
            console.log('');
        }
    }
}
