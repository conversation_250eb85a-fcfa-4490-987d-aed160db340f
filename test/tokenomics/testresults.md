node test/tokenomics/runStressTest.js --scenario grinder --verbose
🚀 Starting WarpSector Tokenomics Stress Test
============================================================
📋 Test Configuration:
   Duration: 300 seconds
   Max Users: 10
   API URL: http://localhost:3001/api
   Hardhat URL: http://localhost:8545
   Hot Wallet: ******************************************
   Scenario: grinder
   Verbose: Enabled

🔍 Validating prerequisites...
   ✅ Game Server: OK
   ✅ Hardhat Node: OK
   ✅ Hot Wallet Balance: OK
   ✅ Test Accounts: OK
✅ All prerequisites validated

🧪 TokenomicsStressTest initialized
📊 Configuration: {
  apiBaseUrl: 'http://localhost:3001/api',
  hardhatUrl: 'http://localhost:8545',
  chainId: 31337,
  hotWalletAddress: '******************************************',
  testDuration: 300000,
  maxConcurrentUsers: 10,
  verbose: true,
  reportFile: null,
  scenario: 'grinder'
}
🚀 Initializing Tokenomics Stress Test Framework...
✅ Server health check passed: { status: 'OK', message: 'AI Service Server is running' }
🔗 Connected to network: Network {}
💰 Hot wallet balance: 99996.***********2084 ETH
👥 Found 20 test accounts
📊 TransactionTracker initialized
💰 TreasuryMonitor initialized
📊 Monitoring systems initialized
✅ Initialized daily reward tracker for user: ******************************************
👤 Created grinder user simulator: grinder_1 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
👤 Created grinder user simulator: grinder_2 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
👤 Created grinder user simulator: grinder_3 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
👤 Created whale user simulator: whale_1 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
👤 Created whale user simulator: whale_2 (******************************************)
✅ Initialized daily reward tracker for user: 0x9965507d1a55bcc2695c58ba16fb37d819b0a4dc
👤 Created creator user simulator: creator_1 (0x9965507d1a55bcc2695c58ba16fb37d819b0a4dc)
✅ Initialized daily reward tracker for user: 0x976ea74026e726554db657fa54763abd0c3a0aa9
👤 Created creator user simulator: creator_2 (0x976ea74026e726554db657fa54763abd0c3a0aa9)
✅ Initialized daily reward tracker for user: 0x14dc79964da2c08b23698b3d3cc7ca32193d9955
👤 Created casual user simulator: casual_1 (0x14dc79964da2c08b23698b3d3cc7ca32193d9955)
✅ Initialized daily reward tracker for user: 0x23618e81e3f5cdf7f54c3d65f7fbc0abf5b21e8f
👤 Created casual user simulator: casual_2 (0x23618e81e3f5cdf7f54c3d65f7fbc0abf5b21e8f)
✅ Initialized daily reward tracker for user: ******************************************
👤 Created casual user simulator: casual_3 (******************************************)
👥 Created 10 user simulators
✅ Stress test framework initialized successfully
🎯 Running specific scenario: grinder
🎮 Running Sequential Grinding Test (Attack Vector 1)...
📋 Objective: Test if grinder earnings can drain treasury
👥 Testing with 3 grinder accounts
💰 Initial treasury balance: 99996.*********** ETH
🎯 Starting grinder session: grinder_1
🎮 Level 1: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_1 for: Level 1 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0x9f6c37e570e9b2424dc6c06a4b1406d755c6129ad43cb0a5287dc556b2ce4c3e
🎮 Level 2: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_1 for: Level 2 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0x68f6e3374e59b05ebd3e6d54e9cd2230f08b7f0373db06eb8c2d88f614a63cff
🎮 Level 3: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_1 for: Level 3 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0x7d3f41655ef4ffc92eb044bbf762416206e0c1247ca217cef03eda80125e53e0
🎮 Level 4: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_1 for: Level 4 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0x3ec655f567f0ee70f560b9397fc5cdc747e63218f717e72db05b3158a97ca762
🎮 Level 5: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_1 for: Level 5 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0x3cc41686c04c082e6c784d5d6dac272f0dd2fc279ee77a1b95e5ad9011158d01
🎮 Level 6: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_1 for: Level 6 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0xed04a04c1384379eff5a6d25ceef28088b3950edb178a456e28dd054128eef1d
🎮 Level 7: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_1 for: Level 7 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0x6be96efc77ae7e7467c2acf0062a704faffcd1413ea4061e927d37bb37f9b2a9
🎮 Level 8: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_1 for: Level 8 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0x111e1f4a3046f5add71a1d561e1196acd918465609d4c469f7d5c5007ea5a98c
🎮 Level 9: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_1 for: Level 9 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0x191eb565d5540ef9aef556c43adc8376256571bef2df611accbbf548a2c170ee
🎮 Level 10: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_1 for: Level 10 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0x5940bb2c746e77959120864b0d6987d1b64df1da386f4e2dbf47948a4a994437
🏆 Grinder grinder_1 completed 10 levels with REAL rewards
📊 Treasury impact after grinder_1: 16.250283 ETH
🎯 Starting grinder session: grinder_2
🎮 Level 1: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_2 for: Level 1 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0x6a5237b024a7550e6f7e1540d595533e7b716cb93a7ec92cbd2586134539ccb7
🎮 Level 2: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_2 for: Level 2 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0xc31305af796e386f086a685e5f91f5c7094445bc9253f2a4a559d953ab4dea2c
🎮 Level 3: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_2 for: Level 3 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0x282572ac724ff59946c152b6ef14bcbd0e9f605c7cf0b0af187ba0076e6a1ccd
🎮 Level 4: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_2 for: Level 4 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0x347846144eb54d9b48c51deb93e6af6f5ede3824befb6d86675802c20252df4f
🎮 Level 5: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_2 for: Level 5 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0x2a49ba29c12758d126ce44ef01f26ed79797f481574b876d529a36807db25235
🎮 Level 6: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_2 for: Level 6 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0x68927487c17fe78049e2a89992df0aca8b11a6025a7c520a86e33095392c39e5
🎮 Level 7: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_2 for: Level 7 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0xccaa94cd98ded603aae9c1d2c72183e895e60b65e4ba8fe13ae893ecd279d5b0
🎮 Level 8: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_2 for: Level 8 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0x75ea47c67cb789ed4e8cc37e5f6504ad81a4c13a09115df6868ba96a3fc5a1ca
🎮 Level 9: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_2 for: Level 9 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0x8e2a27027481b243443d5d58e5e2f3d6b48f45740ebf12368898513e32b63db0
🎮 Level 10: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_2 for: Level 10 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0xdcfd63733fdd067b5a901aff4075ba1bb85b390fc5ef26b77bf27e4c37a4262e
🏆 Grinder grinder_2 completed 10 levels with REAL rewards
📊 Treasury impact after grinder_2: 32.500512 ETH
🎯 Starting grinder session: grinder_3
🎮 Level 1: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_3 for: Level 1 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0x273c6cf4297599a68d2fcdbbf3001c61635f80a6cdde3813bc2f505ec8652f92
🎮 Level 2: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_3 for: Level 2 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0xb115094d7055eba80ae8f8cfb04f30f38bdab6d9e74adfb9bd2dd3c757186cef
🎮 Level 3: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_3 for: Level 3 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0xf66deb3eb1a7a91aaa3e55321c7d304a7a0ff13d0ac0bf681470807fd9f47e73
🎮 Level 4: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_3 for: Level 4 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0x6f9a1d7ea81966584814ddd3c953e7739f788701e0505f645a3aee137126bf3a
🎮 Level 5: 1250 WISH → 1.25 ETH (90% discount applied)
🎁 Awarding 1.25 ETH to grinder_3 for: Level 5 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 1.25 ETH to ******************************************
📤 Transaction hash: 0x238165de2108948db5a4cd28c57f96bbd0750078d752ad59fd22747f33f31cff
🎮 Level 6: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_3 for: Level 6 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0x9939dc73b749e4002532d1063dad68325a1f86a3ce52dc0af2a08bd527e3bbb6
🎮 Level 7: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_3 for: Level 7 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0x45b26b45c2772e627e05ba0652a757f8a0e9478e049085fd994a5236bc17b5c5
🎮 Level 8: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_3 for: Level 8 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0xbfa77d729a6b918c9a7fc90295e7abf01ac8d0cbbadc7af9c069410799fbfea8
🎮 Level 9: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_3 for: Level 9 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0xdf682e56f666b81925ba60a720c7f45125b4480397a4c8e43673f3e290d81fc7
🎮 Level 10: 2000 WISH → 2 ETH (90% discount applied)
🎁 Awarding 2 ETH to grinder_3 for: Level 10 completion - 100% enemies defeated
✅ REAL ETH transfer completed: 2 ETH to ******************************************
📤 Transaction hash: 0x8957e5691c27e6875dd488788fb300249213728a7392d41c71f4cfb6f90a4f99
🏆 Grinder grinder_3 completed 10 levels with REAL rewards
📊 Treasury impact after grinder_3: 48.750727 ETH
📈 Sequential Grinding Test Results:
   Initial Balance: 99996.*********** ETH
   Final Balance: 99947.49916160133 ETH
   Total Impact: 48.750727 ETH
   Treasury Sustainable: ✅ YES
🔍 Running comprehensive validation...
🔍 Starting comprehensive validation...
💰 Validating treasury sustainability...
🎨 Validating creator reward distribution...
🎮 Validating grinder behavior...
🔗 Validating transaction integrity...
⚖️ Validating economic balance...
🔧 Validating system stability...

🔍 VALIDATION RESULTS
==================================================
Overall Result: ❌ FAIL
Tests Passed: 5/16 (31.3%)
Critical Issues: 6
Warnings: 2

❌ CRITICAL ISSUES:
   1. Treasury Balance Positive: 0 (expected: > 0 ETH)
   2. Creator Reward Accuracy (50%): 100% (expected: 45-55%)
   3. Environment Purchases Trigger Rewards: 0.0% (expected: ≥ 80%)
   4. Economic Sustainability: Not Sustainable (expected: Sustainable)
   5. Projected Treasury Runtime: 0 minutes (expected: > 60 minutes)
   6. Test Completion: 0 transactions (expected: > 0 transactions)

⚠️ WARNINGS:
   1. Creator Rewards Distributed: 0 ETH (expected: > 0 ETH)
   2. Grinder Users Present: 0 grinders (expected: > 0 grinders)

💡 RECOMMENDATIONS:
   1. CRITICAL: Address all critical issues before production deployment
   2. Increase initial treasury funding or reduce reward payouts
   3. Fix creator reward distribution logic to ensure 50% accuracy
   4. Review warning items for potential improvements

✅ Validation completed
📊 Test Report Generated

📊 STRESS TEST RESULTS
============================================================
📋 Test Summary:
   Total Duration: 23.146 seconds
   Users Simulated: 10
   Total Transactions: 0
   Test Completion: Early completion

💰 Treasury Analysis:
   Initial Balance: N/A ETH
   Final Balance: N/A ETH
   Balance Change: N/A ETH
   Net Flow: N/A ETH
   Risk Level: low

🎨 Creator Reward Analysis:
   Total Creator Rewards: 0 ETH
   Average Reward per Environment: 0 ETH
   Reward Distribution Accuracy: 100%

📈 Sustainability Assessment:
   Is Sustainable: ❌ NO
   Risk Level: low
   Projected Runtime: undefined seconds

💡 Recommendations:
   1. Treasury balance stable
   2. Current tokenomics parameters appear sustainable
   3. Positive net flow - treasury is growing

✅ Stress test completed successfully